server:
    port: 8082
    # # TODO 用作tomcat测试后面需要删除
    # tomcat:
    #     threads:
    #         max: 50 # 允许的最大线程数
    #     accept-count: 50 # 最大排队等待数量
    #     max-connections: 100 # 允许的最大连接
feign:
    okhttp:
        enabled: true # 开启OKHttp连接池支持
    sentinel:
        enabled: true # 开启feign对sentinel的支持
hm:
    swagger:
        title: 购物车服务接口文档
        package: com.hmall.cart.controller
    db:
        database: hm-cart
spring:
    cloud:
        sentinel:
            transport:
                dashboard: localhost:8090
            http-method-specify: true # 开启sentinel请求方式前缀
