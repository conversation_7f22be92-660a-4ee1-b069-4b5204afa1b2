package com.hmall.cart.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmall.api.client.ItemClient;
import com.hmall.api.dto.ItemDTO;
import com.hmall.cart.config.CartProperties;
import com.hmall.cart.domain.dto.CartFormDTO;
import com.hmall.cart.domain.po.Cart;
import com.hmall.cart.domain.vo.CartVO;
import com.hmall.cart.mapper.CartMapper;
import com.hmall.cart.service.ICartService;
import com.hmall.common.exception.BadRequestException;
import com.hmall.common.exception.BizIllegalException;
import com.hmall.common.utils.BeanUtils;
import com.hmall.common.utils.CollUtils;
import com.hmall.common.utils.UserContext;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 订单详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
@RequiredArgsConstructor
public class CartServiceImpl extends ServiceImpl<CartMapper, Cart> implements ICartService {

    /**
     * 使用final关键字为了@RequiredArgsConstructor可以扫描注入
     */
    private final RestTemplate restTemplate;

    private final DiscoveryClient discoveryClient;

    private final ItemClient itemClient;

    private final RabbitTemplate rabbitTemplate;

    @Autowired
    private CartProperties cartProperties;

    @PostConstruct
    public void printMessageConverterType() {
        MessageConverter converter = rabbitTemplate.getMessageConverter();
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        System.out.println("当前RabbitTemplate的消息转换器类型: " + converter.getClass().getName());
        // 或者更友好的输出
        // System.out.println("RabbitTemplate MessageConverter = " + converter);
    }

    @Override
    public void addItem2Cart(CartFormDTO cartFormDTO) {
        // 1.获取登录用户
        Long userId = UserContext.getUser();

        // 2.判断是否已经存在
        if (checkItemExists(cartFormDTO.getItemId(), userId)) {
            // 2.1.存在，则更新数量
            baseMapper.updateNum(cartFormDTO.getItemId(), userId);
            return;
        }
        // 2.2.不存在，判断是否超过购物车数量
        checkCartsFull(userId);

        // 3.新增购物车条目
        // 3.1.转换PO
        Cart cart = BeanUtils.copyBean(cartFormDTO, Cart.class);
        // 3.2.保存当前用户
        cart.setUserId(userId);
        // 3.3.保存到数据库
        save(cart);
    }

    @Override
    public List<CartVO> queryMyCarts() {
        // 1.查询我的购物车列表
        List<Cart> carts = lambdaQuery().eq(Cart::getUserId, UserContext.getUser()).list();
        if (CollUtils.isEmpty(carts)) {
            return CollUtils.emptyList();
        }

        // 2.转换VO
        List<CartVO> vos = BeanUtils.copyList(carts, CartVO.class);

        // 3.处理VO中的商品信息
        handleCartItems(vos);

        // 4.返回
        return vos;
    }

    private void handleCartItems(List<CartVO> vos) {

        // 1.获取商品id
        Set<Long> itemIds = vos.stream().map(CartVO::getItemId).collect(Collectors.toSet());

        // // 2.查询商品
        // List<ItemDTO> items = itemService.queryItemByIds(itemIds);

        // // 获取实例列表
        // List<ServiceInstance> instances =
        // discoveryClient.getInstances("item-service");

        // // 负载均衡--随机数实现
        // ServiceInstance serviceInstance =
        // instances.get(RandomUtil.randomInt(instances.size()));

        // // 2.查询商品
        // ResponseEntity<List<ItemDTO>> responseEntity = restTemplate.exchange(
        // serviceInstance.getUri() + "/items?ids={ids}", // 使用实例的uri
        // HttpMethod.GET,
        // null,
        // new ParameterizedTypeReference<List<ItemDTO>>() {
        // },
        // Map.of("ids", CollUtil.join(itemIds, ",")));

        // // 判断返回结果
        // if (responseEntity == null) {
        // // api返回是空值
        // return;
        // }

        // List<ItemDTO> items = responseEntity.getBody();

        List<ItemDTO> items = itemClient.queryItemByIds(itemIds);

        if (CollUtils.isEmpty(items)) {
            throw new BadRequestException("购物车中商品不存在！");
        }

        // 3.转为 id 到 item的map
        Map<Long, ItemDTO> itemMap = items.stream().collect(Collectors.toMap(ItemDTO::getId,
                Function.identity()));
        // 4.写入vo
        for (CartVO v : vos) {
            ItemDTO item = itemMap.get(v.getItemId());
            if (item == null) {
                continue;
            }
            v.setNewPrice(item.getPrice());
            v.setStatus(item.getStatus());
            v.setStock(item.getStock());
        }
    }

    @Override
    @Transactional
    public void removeByItemIds(Collection<Long> itemIds) {
        // 1.构建删除条件，userId和itemId
        QueryWrapper<Cart> queryWrapper = new QueryWrapper<Cart>();
        queryWrapper.lambda()
                .eq(Cart::getUserId, UserContext.getUser())
                .in(Cart::getItemId, itemIds);
        // 2.删除
        remove(queryWrapper);
    }

    private void checkCartsFull(Long userId) {
        // 使用热更新配置购物车数量
        Long count = lambdaQuery().eq(Cart::getUserId, userId).count();
        if (count >= cartProperties.getMaxAmount()) {
            throw new BizIllegalException(StrUtil.format("用户购物车课程不能超过{}", cartProperties.getMaxAmount()));
        }
    }

    private boolean checkItemExists(Long itemId, Long userId) {
        Long count = lambdaQuery()
                .eq(Cart::getUserId, userId)
                .eq(Cart::getItemId, itemId)
                .count();
        return count > 0;
    }
}
