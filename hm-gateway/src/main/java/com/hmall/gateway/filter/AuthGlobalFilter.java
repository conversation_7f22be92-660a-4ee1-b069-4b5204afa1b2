package com.hmall.gateway.filter;

import java.util.List;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;

import com.hmall.gateway.config.AuthProperties;
import com.hmall.gateway.util.JwtTool;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(AuthProperties.class)
public class AuthGlobalFilter implements GlobalFilter, Ordered {

    private final JwtTool jwtTool;

    private final AuthProperties authProperties;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 定义该filter的顺序,数字越小pre的顺序越靠前,post则反之
     */
    @Override
    public int getOrder() {
        // 路由转发的filter的order是MAX_INT,因此我们只要数字比这个小就可以完成在转换发前先走的这个filter
        return 0;
    }

    /**
     * 定义filter主体
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 1.获取request对象
        ServerHttpRequest request = exchange.getRequest();

        // 2.判断是否在需要拦截路径内
        if (isExclude(request.getPath().toString())) {
            // 2.1 不在拦截路径内,直接放行
            return chain.filter(exchange);
        }

        // 2.2在拦截路径内,执行后续逻辑
        // 3.jwt解析用户id
        String token = null;
        List<String> headers = request.getHeaders().get("authorization");
        if (CollUtil.isNotEmpty(headers)) {
            token = headers.get(0);
        }

        Long userId = null;
        try {
            // 尝试获取userId
            userId = jwtTool.parseToken(token);
        } catch (Exception e) {
            // 获取失败不放行
            ServerHttpResponse response = exchange.getResponse();
            response.setRawStatusCode(401);
            return response.setComplete();
        }

        // 4.用户id信息重新封装至头部中
        String userInfo = userId.toString();
        // 重新组装exchange,携带用户信息
        ServerWebExchange ex = exchange.mutate()
                .request(req -> req.header("user-info", userInfo))
                .build();
        // 放行
        return chain.filter(ex);
    }

    /**
     * 检查路径是否在过滤路径内
     * 
     * @param string
     * @return
     */
    private boolean isExclude(String antPath) {
        List<String> excludePaths = authProperties.getExcludePaths();

        for (String excludePath : excludePaths) {
            if (antPathMatcher.match(excludePath, antPath)) {
                // 路径匹配,返回true
                return true;
            }
        }

        // 路径不匹配,返回false
        return false;
    }

}
