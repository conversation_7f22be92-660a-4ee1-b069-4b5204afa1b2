spring:
    application:
        name: item-service # 服务名称
    profiles:
        active: dev
    cloud:
        nacos:
            server-addr: localhost:8848 # nacos地址
            config:
                file-extension: yaml # 文件后缀名
                shared-configs: # 共享配置
                    - dataId: shared-jdbc.yaml # 共享mybatis配置
                    - dataId: shared-log.yaml # 共享日志配置
                    - dataId: shared-swagger.yaml # 共享日志配置
                    - dataId: shared-seata.yaml # 共享seata配置
