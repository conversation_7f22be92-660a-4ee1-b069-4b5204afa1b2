package com.hmall.api.client.fallback;

import java.util.Collection;
import java.util.List;

import org.springframework.cloud.openfeign.FallbackFactory;

import com.hmall.api.client.ItemClient;
import com.hmall.api.dto.ItemDTO;
import com.hmall.api.dto.OrderDetailDTO;
import com.hmall.common.exception.BizIllegalException;
import com.hmall.common.utils.CollUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 用于ItemClient在远程调用时因为请求失败(限流/超时/熔断等)做的兜底方案
 */

@Slf4j
public class ItemClientFallback implements FallbackFactory {

    @Override
    public ItemClient create(Throwable cause) {
        // 返回这个新的调用ItemClient客户端,然后走的是这个里面的逻辑
        return new ItemClient() {

            @Override
            public List<ItemDTO> queryItemByIds(Collection<Long> ids) {
                log.error("远程调用ItemClient#queryItemByIds方法出现异常，参数：{}", ids, cause);
                // 查询购物车允许失败，查询失败，返回空集合
                return CollUtils.emptyList();
            }

            @Override
            public void deductStock(List<OrderDetailDTO> items) {
                log.error("库存扣减业务需要触发事务回滚，查询失败:{}", items);
                // 库存扣减业务需要触发事务回滚，查询失败，抛出异常--为分布式事务埋下伏笔
                throw new BizIllegalException(cause);
            }

        };
    }

}
