package com.hmall.trade.listener;

import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.hmall.trade.service.IOrderService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class PayStatusListener {

    private final IOrderService orderService;

    /**
     * 监听支付成功消息
     * 将订单标记为支付成功
     * 
     * @param orderId
     */
    @RabbitListener(bindings = @QueueBinding(value = @Queue(name = "trade.pay.success.queue", durable = "true"), exchange = @Exchange(name = "pay.direct"), key = "pay.success"))
    public void listenPaySuccess(Long orderId) {
        log.info("收到消息:{}", orderId);
        log.info("收到消息:{}", orderId);
        log.info("收到消息:{}", orderId);
        log.info("收到消息:{}", orderId);
        orderService.markOrderPaySuccess(orderId);
    }
}